// electron/main.ts
import { app, BrowserWindow } from 'electron';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
// ES Module'de __dirname equivalent'i
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Vite sunucumuzun adresi (geliştirme modunda)
const isDev = process.env.NODE_ENV === 'development';
const VITE_DEV_SERVER_URL = isDev ? process.env.VITE_DEV_SERVER_URL : undefined;
function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            // preload: path.join(__dirname, 'preload.mjs'), // Şimdilik preload'ı devre dışı bırak
            nodeIntegration: true, // Node.js API'lerini renderer'da etkinleştir (güvenlik riski ta<PERSON>ı<PERSON>bilir, dikka<PERSON><PERSON> k<PERSON>)
            contextIsolation: false, // Context Isolation'ı devre dışı bırak (geliştirme kolaylığı için, üretimde önerilmez)
        },
    });
    // Geliştirme modunda Vite sunucusunu yükle, üretimde build edilmiş dosyayı yükle
    if (VITE_DEV_SERVER_URL) {
        mainWindow.loadURL(VITE_DEV_SERVER_URL);
        if (isDev) {
            mainWindow.webContents.openDevTools(); // Geliştirme araçlarını aç
        }
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../../dist/index.html')); // Üretim için
    }
}
// Uygulama hazır olduğunda pencere oluştur
app.whenReady().then(() => {
    createWindow();
});
// Tüm pencereler kapatıldığında uygulamayı kapat
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});
// Uygulama aktif olduğunda (macOS için)
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
