/*
  Warnings:

  - Added the required column `reservationEndDateTime` to the `Reservation` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reservationStartDateTime` to the `Reservation` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "CourierLocation" ALTER COLUMN "expiresAt" SET DEFAULT NOW() + INTERVAL '7 days';

-- AlterTable
ALTER TABLE "Reservation" ADD COLUMN     "reservationEndDateTime" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "reservationStartDateTime" TIMESTAMP(3) NOT NULL;
