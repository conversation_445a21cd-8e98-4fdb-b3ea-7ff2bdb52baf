
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.12.0
 * Query Engine version: 8047c96bbd92db98a2abc7c9323ce77c02c89dbc
 */
Prisma.prismaVersion = {
  client: "6.12.0",
  engine: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  taxNumber: 'taxNumber',
  taxOffice: 'taxOffice',
  address: 'address',
  phone: 'phone',
  email: 'email',
  logo: 'logo',
  website: 'website',
  eArchiveUsername: 'eArchiveUsername',
  eArchivePassword: 'eArchivePassword',
  eInvoiceUsername: 'eInvoiceUsername',
  eInvoicePassword: 'eInvoicePassword',
  smsProvider: 'smsProvider',
  smsApiKey: 'smsApiKey',
  smsApiSecret: 'smsApiSecret',
  smsSenderName: 'smsSenderName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  code: 'code',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  latitude: 'latitude',
  longitude: 'longitude',
  serverIp: 'serverIp',
  serverPort: 'serverPort',
  isMainBranch: 'isMainBranch',
  openingTime: 'openingTime',
  closingTime: 'closingTime',
  workingDays: 'workingDays',
  cashRegisterId: 'cashRegisterId',
  posTerminalId: 'posTerminalId',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.SyncLogScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  syncType: 'syncType',
  direction: 'direction',
  recordCount: 'recordCount',
  successCount: 'successCount',
  failureCount: 'failureCount',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  error: 'error',
  details: 'details'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  branchId: 'branchId',
  username: 'username',
  password: 'password',
  pin: 'pin',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  avatar: 'avatar',
  role: 'role',
  permissions: 'permissions',
  employeeCode: 'employeeCode',
  hireDate: 'hireDate',
  birthDate: 'birthDate',
  nationalId: 'nationalId',
  vehicleType: 'vehicleType',
  vehiclePlate: 'vehiclePlate',
  active: 'active',
  lastLoginAt: 'lastLoginAt',
  refreshToken: 'refreshToken',
  failedLoginCount: 'failedLoginCount',
  lockedUntil: 'lockedUntil',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  branchId: 'branchId',
  token: 'token',
  deviceInfo: 'deviceInfo',
  ipAddress: 'ipAddress',
  startedAt: 'startedAt',
  endedAt: 'endedAt',
  lastActivityAt: 'lastActivityAt'
};

exports.Prisma.ClockRecordScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  branchId: 'branchId',
  clockIn: 'clockIn',
  clockOut: 'clockOut',
  breakStart: 'breakStart',
  breakEnd: 'breakEnd',
  totalBreakMinutes: 'totalBreakMinutes'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  parentId: 'parentId',
  name: 'name',
  description: 'description',
  image: 'image',
  color: 'color',
  icon: 'icon',
  showInKitchen: 'showInKitchen',
  preparationTime: 'preparationTime',
  displayOrder: 'displayOrder',
  active: 'active',
  showInMenu: 'showInMenu',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  printerGroupId: 'printerGroupId'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  categoryId: 'categoryId',
  code: 'code',
  barcode: 'barcode',
  name: 'name',
  description: 'description',
  shortDescription: 'shortDescription',
  image: 'image',
  images: 'images',
  basePrice: 'basePrice',
  taxId: 'taxId',
  costPrice: 'costPrice',
  profitMargin: 'profitMargin',
  trackStock: 'trackStock',
  unit: 'unit',
  criticalStock: 'criticalStock',
  available: 'available',
  sellable: 'sellable',
  preparationTime: 'preparationTime',
  calories: 'calories',
  allergens: 'allergens',
  hasVariants: 'hasVariants',
  hasModifiers: 'hasModifiers',
  showInMenu: 'showInMenu',
  featured: 'featured',
  displayOrder: 'displayOrder',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  syncId: 'syncId',
  lastSyncAt: 'lastSyncAt'
};

exports.Prisma.ProductVariantScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  code: 'code',
  sku: 'sku',
  barcode: 'barcode',
  price: 'price',
  costPrice: 'costPrice',
  displayOrder: 'displayOrder',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.ComboItemScalarFieldEnum = {
  id: 'id',
  parentProductId: 'parentProductId',
  childProductId: 'childProductId',
  quantity: 'quantity'
};

exports.Prisma.ModifierGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  minSelection: 'minSelection',
  maxSelection: 'maxSelection',
  required: 'required',
  freeSelection: 'freeSelection',
  displayOrder: 'displayOrder',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.ModifierScalarFieldEnum = {
  id: 'id',
  groupId: 'groupId',
  name: 'name',
  price: 'price',
  maxQuantity: 'maxQuantity',
  inventoryItemId: 'inventoryItemId',
  displayOrder: 'displayOrder',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.ProductModifierGroupScalarFieldEnum = {
  productId: 'productId',
  modifierGroupId: 'modifierGroupId',
  displayOrder: 'displayOrder'
};

exports.Prisma.InventoryItemScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  code: 'code',
  barcode: 'barcode',
  unit: 'unit',
  currentStock: 'currentStock',
  reservedStock: 'reservedStock',
  availableStock: 'availableStock',
  criticalLevel: 'criticalLevel',
  optimalLevel: 'optimalLevel',
  lastCost: 'lastCost',
  averageCost: 'averageCost',
  supplier: 'supplier',
  supplierCode: 'supplierCode',
  location: 'location',
  expiryDate: 'expiryDate',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.RecipeScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  yield: 'yield',
  preparationSteps: 'preparationSteps',
  preparationTime: 'preparationTime',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.RecipeItemScalarFieldEnum = {
  id: 'id',
  recipeId: 'recipeId',
  inventoryItemId: 'inventoryItemId',
  quantity: 'quantity',
  unit: 'unit',
  wastagePercent: 'wastagePercent'
};

exports.Prisma.TableAreaScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  name: 'name',
  description: 'description',
  displayOrder: 'displayOrder',
  active: 'active',
  smokingAllowed: 'smokingAllowed',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.TableScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  areaId: 'areaId',
  number: 'number',
  name: 'name',
  capacity: 'capacity',
  minCapacity: 'minCapacity',
  positionX: 'positionX',
  positionY: 'positionY',
  width: 'width',
  height: 'height',
  shape: 'shape',
  status: 'status',
  mergedWithIds: 'mergedWithIds',
  isVip: 'isVip',
  qrCode: 'qrCode',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.TableMergeScalarFieldEnum = {
  id: 'id',
  tableId: 'tableId',
  targetId: 'targetId',
  createdAt: 'createdAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  orderNumber: 'orderNumber',
  orderCode: 'orderCode',
  orderType: 'orderType',
  tableId: 'tableId',
  customerCount: 'customerCount',
  customerId: 'customerId',
  customerName: 'customerName',
  customerPhone: 'customerPhone',
  deliveryAddress: 'deliveryAddress',
  deliveryNote: 'deliveryNote',
  status: 'status',
  paymentStatus: 'paymentStatus',
  mergeTargetId: 'mergeTargetId',
  splitFromId: 'splitFromId',
  subtotal: 'subtotal',
  discountAmount: 'discountAmount',
  discountRate: 'discountRate',
  discountReason: 'discountReason',
  serviceCharge: 'serviceCharge',
  deliveryFee: 'deliveryFee',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  paidAmount: 'paidAmount',
  changeAmount: 'changeAmount',
  tipAmount: 'tipAmount',
  roundingAmount: 'roundingAmount',
  waiterId: 'waiterId',
  cashierId: 'cashierId',
  courierId: 'courierId',
  orderNote: 'orderNote',
  kitchenNote: 'kitchenNote',
  internalNote: 'internalNote',
  orderedAt: 'orderedAt',
  confirmedAt: 'confirmedAt',
  preparingAt: 'preparingAt',
  preparedAt: 'preparedAt',
  servedAt: 'servedAt',
  deliveredAt: 'deliveredAt',
  completedAt: 'completedAt',
  cancelledAt: 'cancelledAt',
  estimatedTime: 'estimatedTime',
  actualTime: 'actualTime',
  onlinePlatformId: 'onlinePlatformId',
  platformOrderId: 'platformOrderId',
  platformOrderNo: 'platformOrderNo',
  syncId: 'syncId',
  lastSyncAt: 'lastSyncAt',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  variantId: 'variantId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  costPrice: 'costPrice',
  discountAmount: 'discountAmount',
  discountRate: 'discountRate',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  status: 'status',
  sentToKitchenAt: 'sentToKitchenAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  servedAt: 'servedAt',
  cancelledAt: 'cancelledAt',
  voidReason: 'voidReason',
  voidedBy: 'voidedBy',
  guestName: 'guestName',
  courseNumber: 'courseNumber',
  note: 'note',
  printCount: 'printCount',
  lastPrintedAt: 'lastPrintedAt',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.OrderItemModifierScalarFieldEnum = {
  id: 'id',
  orderItemId: 'orderItemId',
  modifierId: 'modifierId',
  name: 'name',
  quantity: 'quantity',
  price: 'price'
};

exports.Prisma.PaymentMethodScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  type: 'type',
  commissionRate: 'commissionRate',
  minAmount: 'minAmount',
  maxAmount: 'maxAmount',
  requiresApproval: 'requiresApproval',
  requiresReference: 'requiresReference',
  providerName: 'providerName',
  merchantId: 'merchantId',
  terminalId: 'terminalId',
  displayOrder: 'displayOrder',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  paymentMethodId: 'paymentMethodId',
  amount: 'amount',
  tipAmount: 'tipAmount',
  changeAmount: 'changeAmount',
  approvalCode: 'approvalCode',
  referenceNo: 'referenceNo',
  maskedCardNumber: 'maskedCardNumber',
  cardHolderName: 'cardHolderName',
  installments: 'installments',
  transactionId: 'transactionId',
  gatewayResponse: 'gatewayResponse',
  status: 'status',
  paidAt: 'paidAt',
  refundedAt: 'refundedAt',
  refundAmount: 'refundAmount',
  refundReason: 'refundReason',
  cashMovementId: 'cashMovementId',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.TaxScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  rate: 'rate',
  code: 'code',
  type: 'type',
  isDefault: 'isDefault',
  isIncluded: 'isIncluded',
  active: 'active',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  invoiceType: 'invoiceType',
  serialNo: 'serialNo',
  sequenceNo: 'sequenceNo',
  customerName: 'customerName',
  customerTaxNo: 'customerTaxNo',
  customerTaxOffice: 'customerTaxOffice',
  customerAddress: 'customerAddress',
  customerPhone: 'customerPhone',
  customerEmail: 'customerEmail',
  subtotal: 'subtotal',
  discountAmount: 'discountAmount',
  taxDetails: 'taxDetails',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  totalAmountText: 'totalAmountText',
  uuid: 'uuid',
  eArchiveStatus: 'eArchiveStatus',
  eArchiveResponse: 'eArchiveResponse',
  isCancelled: 'isCancelled',
  cancelReason: 'cancelReason',
  cancelledInvoiceId: 'cancelledInvoiceId',
  pdfUrl: 'pdfUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt',
  printedAt: 'printedAt',
  sentAt: 'sentAt',
  viewedAt: 'viewedAt'
};

exports.Prisma.CashMovementScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  userId: 'userId',
  type: 'type',
  paymentMethodId: 'paymentMethodId',
  amount: 'amount',
  description: 'description',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  previousBalance: 'previousBalance',
  currentBalance: 'currentBalance',
  cashRegisterId: 'cashRegisterId',
  safeId: 'safeId',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  createdAt: 'createdAt'
};

exports.Prisma.ExpenseScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  categoryId: 'categoryId',
  amount: 'amount',
  description: 'description',
  invoiceNo: 'invoiceNo',
  supplierName: 'supplierName',
  paymentMethodId: 'paymentMethodId',
  paidAt: 'paidAt',
  dueDate: 'dueDate',
  isRecurring: 'isRecurring',
  recurringPeriod: 'recurringPeriod',
  attachments: 'attachments',
  createdBy: 'createdBy',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExpenseCategoryScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  parentId: 'parentId',
  budgetLimit: 'budgetLimit',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DailyReportScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  reportDate: 'reportDate',
  reportNo: 'reportNo',
  totalOrders: 'totalOrders',
  totalItems: 'totalItems',
  totalCustomers: 'totalCustomers',
  averageTicket: 'averageTicket',
  grossSales: 'grossSales',
  totalDiscount: 'totalDiscount',
  totalServiceCharge: 'totalServiceCharge',
  netSales: 'netSales',
  totalTax: 'totalTax',
  totalSales: 'totalSales',
  cashSales: 'cashSales',
  creditCardSales: 'creditCardSales',
  debitCardSales: 'debitCardSales',
  mealCardSales: 'mealCardSales',
  otherSales: 'otherSales',
  totalReturns: 'totalReturns',
  totalCancellations: 'totalCancellations',
  openingBalance: 'openingBalance',
  totalCashIn: 'totalCashIn',
  totalCashOut: 'totalCashOut',
  expectedBalance: 'expectedBalance',
  actualBalance: 'actualBalance',
  difference: 'difference',
  taxBreakdown: 'taxBreakdown',
  categoryBreakdown: 'categoryBreakdown',
  hourlyBreakdown: 'hourlyBreakdown',
  zReportNo: 'zReportNo',
  fiscalId: 'fiscalId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  printedAt: 'printedAt',
  emailedAt: 'emailedAt'
};

exports.Prisma.StockMovementScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  productId: 'productId',
  inventoryItemId: 'inventoryItemId',
  type: 'type',
  reason: 'reason',
  quantity: 'quantity',
  unit: 'unit',
  unitCost: 'unitCost',
  totalCost: 'totalCost',
  previousCost: 'previousCost',
  newAverageCost: 'newAverageCost',
  previousStock: 'previousStock',
  currentStock: 'currentStock',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  referenceNo: 'referenceNo',
  fromBranchId: 'fromBranchId',
  toBranchId: 'toBranchId',
  supplierId: 'supplierId',
  invoiceNo: 'invoiceNo',
  note: 'note',
  attachments: 'attachments',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt'
};

exports.Prisma.StockCountScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  countDate: 'countDate',
  countType: 'countType',
  status: 'status',
  note: 'note',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  approvedAt: 'approvedAt',
  createdBy: 'createdBy',
  countedBy: 'countedBy',
  approvedBy: 'approvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StockCountItemScalarFieldEnum = {
  id: 'id',
  stockCountId: 'stockCountId',
  inventoryItemId: 'inventoryItemId',
  systemQuantity: 'systemQuantity',
  countedQuantity: 'countedQuantity',
  difference: 'difference',
  unitCost: 'unitCost',
  totalDifference: 'totalDifference',
  note: 'note'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  companyName: 'companyName',
  title: 'title',
  taxNumber: 'taxNumber',
  taxOffice: 'taxOffice',
  phone: 'phone',
  phone2: 'phone2',
  email: 'email',
  address: 'address',
  district: 'district',
  city: 'city',
  country: 'country',
  postalCode: 'postalCode',
  birthDate: 'birthDate',
  gender: 'gender',
  marketingConsent: 'marketingConsent',
  smsConsent: 'smsConsent',
  emailConsent: 'emailConsent',
  loyaltyPoints: 'loyaltyPoints',
  totalSpent: 'totalSpent',
  orderCount: 'orderCount',
  lastOrderDate: 'lastOrderDate',
  currentDebt: 'currentDebt',
  creditLimit: 'creditLimit',
  paymentTerm: 'paymentTerm',
  segment: 'segment',
  tags: 'tags',
  customFields: 'customFields',
  notes: 'notes',
  source: 'source',
  referredBy: 'referredBy',
  blacklisted: 'blacklisted',
  blacklistReason: 'blacklistReason',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.CustomerAddressScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  title: 'title',
  address: 'address',
  district: 'district',
  city: 'city',
  postalCode: 'postalCode',
  directions: 'directions',
  latitude: 'latitude',
  longitude: 'longitude',
  isDefault: 'isDefault'
};

exports.Prisma.CustomerTransactionScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  type: 'type',
  amount: 'amount',
  balance: 'balance',
  description: 'description',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  dueDate: 'dueDate',
  paidAt: 'paidAt',
  createdAt: 'createdAt'
};

exports.Prisma.OnlinePlatformScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  apiUrl: 'apiUrl',
  apiKey: 'apiKey',
  apiSecret: 'apiSecret',
  merchantId: 'merchantId',
  storeId: 'storeId',
  active: 'active',
  autoAccept: 'autoAccept',
  autoReject: 'autoReject',
  commissionRate: 'commissionRate',
  commissionType: 'commissionType',
  syncProducts: 'syncProducts',
  syncInterval: 'syncInterval',
  lastSyncAt: 'lastSyncAt',
  workingHours: 'workingHours',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OnlineOrderScalarFieldEnum = {
  id: 'id',
  platformId: 'platformId',
  orderId: 'orderId',
  platformOrderId: 'platformOrderId',
  platformOrderNo: 'platformOrderNo',
  customerName: 'customerName',
  customerPhone: 'customerPhone',
  customerEmail: 'customerEmail',
  deliveryAddress: 'deliveryAddress',
  deliveryNote: 'deliveryNote',
  orderData: 'orderData',
  status: 'status',
  platformStatus: 'platformStatus',
  subtotal: 'subtotal',
  deliveryFee: 'deliveryFee',
  serviceFee: 'serviceFee',
  discount: 'discount',
  totalAmount: 'totalAmount',
  commissionAmount: 'commissionAmount',
  netAmount: 'netAmount',
  paymentMethod: 'paymentMethod',
  isPaid: 'isPaid',
  orderedAt: 'orderedAt',
  requestedAt: 'requestedAt',
  acceptedAt: 'acceptedAt',
  rejectedAt: 'rejectedAt',
  preparingAt: 'preparingAt',
  readyAt: 'readyAt',
  deliveringAt: 'deliveringAt',
  deliveredAt: 'deliveredAt',
  cancelledAt: 'cancelledAt',
  rejectReason: 'rejectReason',
  cancelReason: 'cancelReason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OnlineProductMappingScalarFieldEnum = {
  id: 'id',
  platformId: 'platformId',
  productId: 'productId',
  platformProductId: 'platformProductId',
  platformBarcode: 'platformBarcode',
  isActive: 'isActive',
  priceOverride: 'priceOverride'
};

exports.Prisma.LoyaltyCardScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  cardNumber: 'cardNumber',
  cardType: 'cardType',
  points: 'points',
  totalEarnedPoints: 'totalEarnedPoints',
  totalSpentPoints: 'totalSpentPoints',
  balance: 'balance',
  totalLoaded: 'totalLoaded',
  discountRate: 'discountRate',
  pin: 'pin',
  issuedAt: 'issuedAt',
  activatedAt: 'activatedAt',
  expiresAt: 'expiresAt',
  lastUsedAt: 'lastUsedAt',
  blocked: 'blocked',
  blockReason: 'blockReason',
  active: 'active'
};

exports.Prisma.LoyaltyTransactionScalarFieldEnum = {
  id: 'id',
  cardId: 'cardId',
  orderId: 'orderId',
  type: 'type',
  points: 'points',
  pointBalance: 'pointBalance',
  amount: 'amount',
  moneyBalance: 'moneyBalance',
  description: 'description',
  baseAmount: 'baseAmount',
  multiplier: 'multiplier',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.ReservationScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  customerId: 'customerId',
  customerName: 'customerName',
  customerPhone: 'customerPhone',
  customerEmail: 'customerEmail',
  reservationDate: 'reservationDate',
  reservationTime: 'reservationTime',
  reservationStartDateTime: 'reservationStartDateTime',
  reservationEndDateTime: 'reservationEndDateTime',
  duration: 'duration',
  guestCount: 'guestCount',
  childCount: 'childCount',
  tableIds: 'tableIds',
  tablePreference: 'tablePreference',
  status: 'status',
  specialRequests: 'specialRequests',
  allergyInfo: 'allergyInfo',
  occasionType: 'occasionType',
  internalNotes: 'internalNotes',
  source: 'source',
  confirmationCode: 'confirmationCode',
  confirmedBy: 'confirmedBy',
  depositRequired: 'depositRequired',
  depositAmount: 'depositAmount',
  depositPaid: 'depositPaid',
  reminderSent: 'reminderSent',
  reminderSentAt: 'reminderSentAt',
  confirmedAt: 'confirmedAt',
  cancelledAt: 'cancelledAt',
  seatedAt: 'seatedAt',
  completedAt: 'completedAt',
  cancelReason: 'cancelReason',
  noShowFee: 'noShowFee',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.QRMenuScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  name: 'name',
  qrCode: 'qrCode',
  shortUrl: 'shortUrl',
  template: 'template',
  primaryColor: 'primaryColor',
  secondaryColor: 'secondaryColor',
  fontFamily: 'fontFamily',
  logoUrl: 'logoUrl',
  coverImageUrl: 'coverImageUrl',
  backgroundUrl: 'backgroundUrl',
  showPrices: 'showPrices',
  showImages: 'showImages',
  showDescriptions: 'showDescriptions',
  showCalories: 'showCalories',
  showAllergens: 'showAllergens',
  allowOrdering: 'allowOrdering',
  minOrderAmount: 'minOrderAmount',
  languages: 'languages',
  defaultLanguage: 'defaultLanguage',
  viewCount: 'viewCount',
  uniqueViewCount: 'uniqueViewCount',
  lastViewedAt: 'lastViewedAt',
  welcomeMessage: 'welcomeMessage',
  footerText: 'footerText',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MenuAccessLogScalarFieldEnum = {
  id: 'id',
  menuId: 'menuId',
  sessionId: 'sessionId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceType: 'deviceType',
  deviceModel: 'deviceModel',
  osName: 'osName',
  osVersion: 'osVersion',
  browserName: 'browserName',
  browserVersion: 'browserVersion',
  country: 'country',
  city: 'city',
  viewDuration: 'viewDuration',
  clickCount: 'clickCount',
  accessedAt: 'accessedAt'
};

exports.Prisma.MenuFeedbackScalarFieldEnum = {
  id: 'id',
  menuId: 'menuId',
  rating: 'rating',
  comment: 'comment',
  customerName: 'customerName',
  customerEmail: 'customerEmail',
  customerPhone: 'customerPhone',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationTemplateScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  channel: 'channel',
  subject: 'subject',
  content: 'content',
  smsLength: 'smsLength',
  smsCredits: 'smsCredits',
  sendTiming: 'sendTiming',
  sendDelay: 'sendDelay',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationLogScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  recipient: 'recipient',
  channel: 'channel',
  status: 'status',
  message: 'message',
  response: 'response',
  sentAt: 'sentAt',
  deliveredAt: 'deliveredAt',
  readAt: 'readAt',
  failedReason: 'failedReason'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp'
};

exports.Prisma.OrderLogScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  userId: 'userId',
  action: 'action',
  details: 'details',
  timestamp: 'timestamp'
};

exports.Prisma.PriceOverrideScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  productId: 'productId',
  variantId: 'variantId',
  overridePrice: 'overridePrice',
  reason: 'reason',
  startDate: 'startDate',
  endDate: 'endDate',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  companyId: 'companyId',
  title: 'title',
  description: 'description',
  assignedToId: 'assignedToId',
  status: 'status',
  priority: 'priority',
  dueDate: 'dueDate',
  completedAt: 'completedAt',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PrinterGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  categoryIds: 'categoryIds'
};

exports.Prisma.PrinterScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  printerGroupId: 'printerGroupId',
  name: 'name',
  type: 'type',
  connectionType: 'connectionType',
  ipAddress: 'ipAddress',
  port: 'port',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CampaignScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  description: 'description',
  campaignType: 'campaignType',
  discountType: 'discountType',
  discountValue: 'discountValue',
  minOrderAmount: 'minOrderAmount',
  maxDiscountAmount: 'maxDiscountAmount',
  startDate: 'startDate',
  endDate: 'endDate',
  usageLimit: 'usageLimit',
  usageLimitPerUser: 'usageLimitPerUser',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CampaignUsageScalarFieldEnum = {
  id: 'id',
  campaignId: 'campaignId',
  orderId: 'orderId',
  customerId: 'customerId',
  usedAt: 'usedAt',
  discountApplied: 'discountApplied',
  pointsEarned: 'pointsEarned'
};

exports.Prisma.CourierLocationScalarFieldEnum = {
  id: 'id',
  courierId: 'courierId',
  branchId: 'branchId',
  latitude: 'latitude',
  longitude: 'longitude',
  timestamp: 'timestamp',
  expiresAt: 'expiresAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  BRANCH_MANAGER: 'BRANCH_MANAGER',
  CASHIER: 'CASHIER',
  WAITER: 'WAITER',
  KITCHEN: 'KITCHEN',
  REPORTER: 'REPORTER',
  COURIER: 'COURIER',
  CUSTOMER_SERVICE: 'CUSTOMER_SERVICE'
};

exports.ProductUnit = exports.$Enums.ProductUnit = {
  PIECE: 'PIECE',
  KG: 'KG',
  GRAM: 'GRAM',
  LITER: 'LITER',
  ML: 'ML',
  PORTION: 'PORTION',
  BOX: 'BOX',
  PACKAGE: 'PACKAGE'
};

exports.TableShape = exports.$Enums.TableShape = {
  RECTANGLE: 'RECTANGLE',
  CIRCLE: 'CIRCLE',
  SQUARE: 'SQUARE',
  OVAL: 'OVAL'
};

exports.TableStatus = exports.$Enums.TableStatus = {
  EMPTY: 'EMPTY',
  OCCUPIED: 'OCCUPIED',
  RESERVED: 'RESERVED',
  CLEANING: 'CLEANING',
  UNAVAILABLE: 'UNAVAILABLE',
  MERGED: 'MERGED'
};

exports.OrderType = exports.$Enums.OrderType = {
  DINE_IN: 'DINE_IN',
  TAKEAWAY: 'TAKEAWAY',
  DELIVERY: 'DELIVERY',
  ONLINE: 'ONLINE',
  CATERING: 'CATERING',
  SELF_SERVICE: 'SELF_SERVICE'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PREPARING: 'PREPARING',
  READY: 'READY',
  SERVING: 'SERVING',
  DELIVERED: 'DELIVERED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  UNPAID: 'UNPAID',
  PENDING: 'PENDING',
  PAID: 'PAID',
  PARTIALLY_PAID: 'PARTIALLY_PAID',
  REFUNDED: 'REFUNDED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  VOIDED: 'VOIDED',
  FAILED: 'FAILED'
};

exports.OrderItemStatus = exports.$Enums.OrderItemStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  PREPARING: 'PREPARING',
  READY: 'READY',
  SERVED: 'SERVED',
  CANCELLED: 'CANCELLED',
  VOID: 'VOID',
  RETURNED: 'RETURNED'
};

exports.PaymentMethodType = exports.$Enums.PaymentMethodType = {
  CASH: 'CASH',
  CREDIT_CARD: 'CREDIT_CARD',
  DEBIT_CARD: 'DEBIT_CARD',
  MEAL_CARD: 'MEAL_CARD',
  MOBILE: 'MOBILE',
  TRANSFER: 'TRANSFER',
  CHECK: 'CHECK',
  CREDIT: 'CREDIT',
  LOYALTY_POINTS: 'LOYALTY_POINTS',
  GIFT_CARD: 'GIFT_CARD',
  OTHER: 'OTHER'
};

exports.TaxType = exports.$Enums.TaxType = {
  VAT: 'VAT',
  OTV: 'OTV',
  OIV: 'OIV',
  DAMGA: 'DAMGA'
};

exports.InvoiceType = exports.$Enums.InvoiceType = {
  RECEIPT: 'RECEIPT',
  INVOICE: 'INVOICE',
  E_ARCHIVE: 'E_ARCHIVE',
  E_INVOICE: 'E_INVOICE',
  PROFORMA: 'PROFORMA',
  RETURN: 'RETURN'
};

exports.EArchiveStatus = exports.$Enums.EArchiveStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
};

exports.CashMovementType = exports.$Enums.CashMovementType = {
  SALE: 'SALE',
  REFUND: 'REFUND',
  EXPENSE: 'EXPENSE',
  INCOME: 'INCOME',
  OPENING: 'OPENING',
  CLOSING: 'CLOSING',
  DEPOSIT: 'DEPOSIT',
  WITHDRAWAL: 'WITHDRAWAL',
  TRANSFER_IN: 'TRANSFER_IN',
  TRANSFER_OUT: 'TRANSFER_OUT',
  SHORTAGE: 'SHORTAGE',
  SURPLUS: 'SURPLUS',
  MODIFIER_CONSUMPTION: 'MODIFIER_CONSUMPTION'
};

exports.StockMovementType = exports.$Enums.StockMovementType = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  RETURN_IN: 'RETURN_IN',
  RETURN_OUT: 'RETURN_OUT',
  WASTE: 'WASTE',
  DAMAGE: 'DAMAGE',
  THEFT: 'THEFT',
  TRANSFER_IN: 'TRANSFER_IN',
  TRANSFER_OUT: 'TRANSFER_OUT',
  ADJUSTMENT: 'ADJUSTMENT',
  PRODUCTION: 'PRODUCTION',
  CONSUMPTION: 'CONSUMPTION',
  SAMPLE: 'SAMPLE',
  GIFT: 'GIFT',
  MODIFIER_CONSUMPTION: 'MODIFIER_CONSUMPTION'
};

exports.StockCountType = exports.$Enums.StockCountType = {
  FULL: 'FULL',
  PARTIAL: 'PARTIAL',
  CYCLE: 'CYCLE',
  SPOT: 'SPOT'
};

exports.StockCountStatus = exports.$Enums.StockCountStatus = {
  DRAFT: 'DRAFT',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  APPROVED: 'APPROVED',
  CANCELLED: 'CANCELLED'
};

exports.CustomerTransactionType = exports.$Enums.CustomerTransactionType = {
  SALE: 'SALE',
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  OPENING: 'OPENING',
  ADJUSTMENT: 'ADJUSTMENT'
};

exports.OnlineOrderStatus = exports.$Enums.OnlineOrderStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  PREPARING: 'PREPARING',
  READY: 'READY',
  DELIVERING: 'DELIVERING',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED'
};

exports.LoyaltyCardType = exports.$Enums.LoyaltyCardType = {
  STANDARD: 'STANDARD',
  SILVER: 'SILVER',
  GOLD: 'GOLD',
  PLATINUM: 'PLATINUM',
  VIP: 'VIP',
  EMPLOYEE: 'EMPLOYEE',
  GIFT: 'GIFT'
};

exports.LoyaltyTransactionType = exports.$Enums.LoyaltyTransactionType = {
  EARN_PURCHASE: 'EARN_PURCHASE',
  EARN_BONUS: 'EARN_BONUS',
  EARN_CAMPAIGN: 'EARN_CAMPAIGN',
  EARN_BIRTHDAY: 'EARN_BIRTHDAY',
  EARN_REFERRAL: 'EARN_REFERRAL',
  SPEND_DISCOUNT: 'SPEND_DISCOUNT',
  SPEND_PRODUCT: 'SPEND_PRODUCT',
  LOAD_BALANCE: 'LOAD_BALANCE',
  USE_BALANCE: 'USE_BALANCE',
  TRANSFER_IN: 'TRANSFER_IN',
  TRANSFER_OUT: 'TRANSFER_OUT',
  EXPIRE: 'EXPIRE',
  ADJUSTMENT: 'ADJUSTMENT'
};

exports.ReservationStatus = exports.$Enums.ReservationStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  CANCELLED: 'CANCELLED',
  SEATED: 'SEATED',
  COMPLETED: 'COMPLETED',
  NO_SHOW: 'NO_SHOW',
  WAITLIST: 'WAITLIST'
};

exports.ReservationSource = exports.$Enums.ReservationSource = {
  PHONE: 'PHONE',
  WALK_IN: 'WALK_IN',
  WEBSITE: 'WEBSITE',
  MOBILE_APP: 'MOBILE_APP',
  THIRD_PARTY: 'THIRD_PARTY',
  SOCIAL_MEDIA: 'SOCIAL_MEDIA'
};

exports.NotificationChannel = exports.$Enums.NotificationChannel = {
  SMS: 'SMS',
  EMAIL: 'EMAIL',
  PUSH_NOTIFICATION: 'PUSH_NOTIFICATION',
  IN_APP: 'IN_APP'
};

exports.NotificationStatus = exports.$Enums.NotificationStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  DELIVERED: 'DELIVERED',
  READ: 'READ',
  FAILED: 'FAILED',
  BOUNCED: 'BOUNCED',
  CANCELLED: 'CANCELLED'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  ON_HOLD: 'ON_HOLD'
};

exports.TaskPriority = exports.$Enums.TaskPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.PrinterType = exports.$Enums.PrinterType = {
  THERMAL: 'THERMAL',
  DOT_MATRIX: 'DOT_MATRIX',
  A4: 'A4'
};

exports.CampaignType = exports.$Enums.CampaignType = {
  DISCOUNT: 'DISCOUNT',
  LOYALTY_POINT_BONUS: 'LOYALTY_POINT_BONUS',
  FREE_PRODUCT: 'FREE_PRODUCT',
  BOGO: 'BOGO'
};

exports.DiscountType = exports.$Enums.DiscountType = {
  PERCENTAGE: 'PERCENTAGE',
  FIXED_AMOUNT: 'FIXED_AMOUNT'
};

exports.Prisma.ModelName = {
  Company: 'Company',
  Branch: 'Branch',
  SyncLog: 'SyncLog',
  User: 'User',
  Session: 'Session',
  ClockRecord: 'ClockRecord',
  Category: 'Category',
  Product: 'Product',
  ProductVariant: 'ProductVariant',
  ComboItem: 'ComboItem',
  ModifierGroup: 'ModifierGroup',
  Modifier: 'Modifier',
  ProductModifierGroup: 'ProductModifierGroup',
  InventoryItem: 'InventoryItem',
  Recipe: 'Recipe',
  RecipeItem: 'RecipeItem',
  TableArea: 'TableArea',
  Table: 'Table',
  TableMerge: 'TableMerge',
  Order: 'Order',
  OrderItem: 'OrderItem',
  OrderItemModifier: 'OrderItemModifier',
  PaymentMethod: 'PaymentMethod',
  Payment: 'Payment',
  Tax: 'Tax',
  Invoice: 'Invoice',
  CashMovement: 'CashMovement',
  Expense: 'Expense',
  ExpenseCategory: 'ExpenseCategory',
  DailyReport: 'DailyReport',
  StockMovement: 'StockMovement',
  StockCount: 'StockCount',
  StockCountItem: 'StockCountItem',
  Customer: 'Customer',
  CustomerAddress: 'CustomerAddress',
  CustomerTransaction: 'CustomerTransaction',
  OnlinePlatform: 'OnlinePlatform',
  OnlineOrder: 'OnlineOrder',
  OnlineProductMapping: 'OnlineProductMapping',
  LoyaltyCard: 'LoyaltyCard',
  LoyaltyTransaction: 'LoyaltyTransaction',
  Reservation: 'Reservation',
  QRMenu: 'QRMenu',
  MenuAccessLog: 'MenuAccessLog',
  MenuFeedback: 'MenuFeedback',
  NotificationTemplate: 'NotificationTemplate',
  NotificationLog: 'NotificationLog',
  AuditLog: 'AuditLog',
  OrderLog: 'OrderLog',
  PriceOverride: 'PriceOverride',
  Task: 'Task',
  PrinterGroup: 'PrinterGroup',
  Printer: 'Printer',
  Campaign: 'Campaign',
  CampaignUsage: 'CampaignUsage',
  CourierLocation: 'CourierLocation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
