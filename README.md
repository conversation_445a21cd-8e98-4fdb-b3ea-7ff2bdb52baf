# 🏪 Atropos POS System

Modern, full-featured Point of Sale (POS) system built with NestJS, React, and Electron.

## 🚀 Features

### Backend (NestJS + Prisma + PostgreSQL)
- **Multi-tenant Architecture** - Support for multiple companies and branches
- **Comprehensive User Management** - Role-based access control (Admin, Manager, Cashier, Waiter, etc.)
- **Product Management** - Categories, variants, modifiers, stock tracking
- **Order Management** - Dine-in, takeaway, delivery orders
- **Payment Processing** - Multiple payment methods, installments
- **Table Management** - Table areas, reservations, merging
- **Customer Management** - Loyalty programs, customer profiles
- **Inventory Management** - Stock movements, recipes, critical stock alerts
- **Reporting** - Daily reports, sales analytics
- **Invoice System** - E-Archive, E-Invoice integration
- **Campaign Management** - Discounts, promotions
- **Notification System** - SMS, email notifications
- **Printer Management** - Receipt and kitchen printers
- **Online Platform Integration** - Third-party delivery platforms

### Frontend Desktop (Electron + React + TypeScript)
- **Cross-platform Desktop App** - Windows, macOS, Linux support
- **Modern React UI** - Built with React 19 and TypeScript
- **Real-time Updates** - Live order tracking and notifications
- **Offline Capability** - Works without internet connection
- **Touch-friendly Interface** - Optimized for touch screens

## 🛠️ Tech Stack

### Backend
- **Framework**: NestJS 11.x
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT + Passport.js
- **Validation**: class-validator
- **Documentation**: Swagger/OpenAPI
- **Testing**: Jest

### Frontend Desktop
- **Framework**: React 19 + TypeScript
- **Desktop**: Electron 37.x
- **Build Tool**: Vite 7.x
- **Styling**: CSS (ready for UI library integration)

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- npm or yarn

### Backend Setup

```bash
cd atropos-backend

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
npx prisma migrate dev

# Generate Prisma client
npx prisma generate

# Start development server
npm run start:dev
```

### Frontend Desktop Setup

```bash
cd atropos-frontend-desktop

# Install dependencies
npm install

# Start development
npm run dev
```

## 🔧 Configuration

### Environment Variables (Backend)

```env
DATABASE_URL="postgresql://username:password@localhost:5432/atropos"
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="24h"
PORT=3000
```

### Environment Variables (Frontend)

```env
VITE_API_URL="http://localhost:3000"
```

## 📚 API Documentation

Once the backend is running, visit:
- **Swagger UI**: http://localhost:3000/api
- **API Base URL**: http://localhost:3000

