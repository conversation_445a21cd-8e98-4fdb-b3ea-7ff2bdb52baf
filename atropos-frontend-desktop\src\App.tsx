// src/App.tsx
import { useState, useEffect } from 'react';
import './App.css'; // Mevcut CSS'i kullanabiliriz

interface Company {
  id: string;
  name: string;
  taxNumber: string;
  taxOffice: string;
  address: string;
  phone: string;
  email?: string;
  // ... diğer Company alanları
}

function App() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Backend API URL'ini ortam değişkeninden al
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; // Fallback olarak localhost:3000

  useEffect(() => {
    // Bileşen yüklendiğinde şirketleri otomatik olarak çek
    fetchCompanies();
  }, []); // Sadece bir kere yüklensin

  const fetchCompanies = async () => {
    setLoading(true);
    setError(null);
    try {
      // Backend API'mize GET isteği gönder
      const response = await fetch(`${API_URL}/company`);
      if (!response.ok) {
        // Hata durumunda HTTP durum kodunu ve mesajını al
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      const data: Company[] = await response.json();
      setCompanies(data);
    } catch (err: any) {
      setError(err.message);
      console.error("Error fetching companies:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="App">
      <h1>Atropos POS - Masaüstü Uygulaması</h1>
      <p>Backend API Bağlantı Testi</p>
      <button onClick={fetchCompanies} disabled={loading}>
        {loading ? 'Şirketler Yükleniyor...' : 'Şirketleri Yeniden Yükle'}
      </button>

      {error && <p style={{ color: 'red' }}>Hata: {error}</p>}

      <h2>Mevcut Şirketler:</h2>
      {companies.length === 0 && !loading && !error && <p>Henüz şirket bulunamadı.</p>}
      {companies.length > 0 && (
        <ul>
          {companies.map((company) => (
            <li key={company.id}>
              <strong>{company.name}</strong> (Vergi No: {company.taxNumber}) - {company.address}
            </li>
          ))}
        </ul>
      )}
      <p>API URL: {API_URL}</p>
    </div>
  );
}

export default App;
